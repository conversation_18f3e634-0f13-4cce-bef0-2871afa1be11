'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'
import connect from '@/connect'
import { MindElixirData, Options } from 'mind-elixir'
import { MindMapItem } from '@/models/list'
// @ts-ignore
import nodeMenu from '@mind-elixir/node-menu-neo'
import { Outliner } from 'react-outliner-neo'
import 'react-outliner-neo/style.css'
import './share-page.css'
import {
  Brain,
  List,
  Maximize2,
  Minimize2,
  Calendar,
  Eye,
  Share2,
  Copy,
  Check
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

const MindElixirReact = dynamic(() => import('@/components/MindElixirReact'), {
  ssr: false,
  loading: () => <LoadingSkeleton />,
})

// 专业加载骨架屏组件
function LoadingSkeleton() {
  return (
    <div className="min-h-screen gradient-bg">
      {/* 固定头部骨架 */}
      <div className="fixed-header border-b border-gray-200/20 dark:border-gray-800/20 glass-effect">
        <div className="container mx-auto px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-2">
                <div className="w-9 h-9 bg-gray-200 dark:bg-gray-700 rounded-xl loading-pulse"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 loading-pulse"></div>
                <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-16 loading-pulse"></div>
              </div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32 loading-pulse"></div>
            </div>
            <div className="flex items-center gap-3">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded-lg w-48 loading-pulse"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded-lg w-10 loading-pulse"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded-lg w-20 loading-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      {/* 头部间距 */}
      <div className="header-spacer"></div>

      {/* 内容骨架 */}
      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 shadow-strong">
              <div className="border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50 p-4">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 loading-pulse"></div>
              </div>
              <div className="h-96 bg-gray-100 dark:bg-gray-800 loading-pulse"></div>
            </div>
          </div>
          <div className="lg:col-span-1">
            <div className="border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 shadow-strong">
              <div className="border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50 p-4">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 loading-pulse"></div>
              </div>
              <div className="p-6 space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full loading-pulse"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 loading-pulse"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 loading-pulse"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 loading-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

type ViewMode = 'mindmap' | 'outline' | 'split'

export default function MapSharePage() {
  const params = useParams()
  const router = useRouter()
  const [mapData, setMapData] = useState<MindElixirData | undefined>(undefined)
  const [mapItem, setMapItem] = useState<MindMapItem | undefined>(undefined)
  const [viewMode, setViewMode] = useState<ViewMode>('split')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)

  const plugins = [nodeMenu]
  const options: Partial<Options> = {
    direction: 2,
    draggable: false,
    editable: false,
    contextMenu: false,
    toolBar: true,
    keypress: false,
  }

  const mapId = params.id as string

  useEffect(() => {
    const fetchMap = async () => {
      try {
        setLoading(true)
        const res = await connect.get<never, { data: MindMapItem }>(
          `/api/public/${mapId}`
        )
        setMapItem(res.data)
        setMapData(res.data.content)
      } catch (error) {
        console.error('Failed to fetch map:', error)
        router.push('/404')
      } finally {
        setLoading(false)
      }
    }

    if (mapId) {
      fetchMap()
    }
  }, [mapId, router])

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return <LoadingSkeleton />
  }

  if (!mapData || !mapItem) {
    return (
      <div className="min-h-screen flex items-center justify-center gradient-bg">
        <div className="text-center glass-effect p-8 rounded-2xl shadow-strong">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 gradient-text">思维导图未找到</h1>
          <p className="text-gray-600 dark:text-gray-400">请检查链接是否正确</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "min-h-screen gradient-bg",
      isFullscreen && "fixed inset-0 z-50"
    )}>
      {/* 固定头部信息 */}
      <div className="fixed-header border-b border-gray-200/20 dark:border-gray-800/20 glass-effect">
        <div className="container mx-auto px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* 标题和元信息 */}
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-2">
                <div className="p-2.5 rounded-xl bg-gray-100 dark:bg-gray-800 shadow-soft">
                  <Brain className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                </div>
                <h1 className="text-xl lg:text-2xl font-semibold gradient-text">
                  {mapItem.name}
                </h1>
                {mapItem.public && (
                  <Badge variant="secondary" className="status-badge bg-emerald-50 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 border border-emerald-200 dark:border-emerald-800">
                    <Eye className="w-3 h-3 mr-1" />
                    公开
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                <div className="flex items-center gap-1.5">
                  <Calendar className="w-3.5 h-3.5" />
                  <span>更新于 {formatDate(mapItem.updatedAt || mapItem.date)}</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-3 mobile-stack mobile-full-width">
              {/* 视图切换 */}
              <div className="flex items-center bg-gray-50 dark:bg-gray-800 rounded-lg p-0.5 shadow-soft border border-gray-200 dark:border-gray-700">
                <Button
                  variant={viewMode === 'mindmap' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('mindmap')}
                  className={cn(
                    "h-8 px-3 text-xs font-medium btn-hover",
                    viewMode === 'mindmap'
                      ? "bg-white dark:bg-gray-700 shadow-soft text-gray-900 dark:text-gray-100"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                  )}
                >
                  <Brain className="w-3.5 h-3.5 mr-1.5" />
                  思维导图
                </Button>
                <Button
                  variant={viewMode === 'outline' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('outline')}
                  className={cn(
                    "h-8 px-3 text-xs font-medium btn-hover",
                    viewMode === 'outline'
                      ? "bg-white dark:bg-gray-700 shadow-soft text-gray-900 dark:text-gray-100"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                  )}
                >
                  <List className="w-3.5 h-3.5 mr-1.5" />
                  大纲
                </Button>
                <Button
                  variant={viewMode === 'split' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('split')}
                  className={cn(
                    "h-8 px-3 text-xs font-medium btn-hover",
                    viewMode === 'split'
                      ? "bg-white dark:bg-gray-700 shadow-soft text-gray-900 dark:text-gray-100"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                  )}
                >
                  分屏
                </Button>
              </div>

              {/* 全屏切换 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="h-8 px-3 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 btn-hover shadow-soft"
              >
                {isFullscreen ? (
                  <Minimize2 className="w-3.5 h-3.5" />
                ) : (
                  <Maximize2 className="w-3.5 h-3.5" />
                )}
              </Button>

              {/* 分享按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyLink}
                className="h-8 px-3 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 btn-hover shadow-soft"
              >
                {copied ? (
                  <Check className="w-3.5 h-3.5 mr-1.5 text-emerald-600" />
                ) : (
                  <Copy className="w-3.5 h-3.5 mr-1.5" />
                )}
                <span className="text-xs font-medium">{copied ? '已复制' : '复制链接'}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 头部间距 */}
      <div className="header-spacer"></div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-6 py-8">
        {viewMode === 'mindmap' && (
          <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 shadow-strong bg-white dark:bg-gray-900 floating-card mindmap-container">
            <CardContent className="p-0">
              <div className="h-[calc(100vh-280px)] min-h-[600px]">
                <MindElixirReact
                  data={mapData}
                  plugins={plugins}
                  options={options}
                  className="h-full w-full"
                  fitPage={true}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {viewMode === 'outline' && (
          <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 shadow-strong bg-white dark:bg-gray-900 floating-card">
            <CardHeader className="border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50">
              <CardTitle className="flex items-center gap-3 text-lg font-semibold text-gray-900 dark:text-gray-100">
                <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700">
                  <List className="w-4 h-4 text-gray-700 dark:text-gray-300" />
                </div>
                思维导图大纲
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[calc(100vh-380px)] min-h-[500px] overflow-auto custom-scrollbar">
                <Outliner data={mapData.nodeData.children!} />
              </div>
            </CardContent>
          </Card>
        )}

        {viewMode === 'split' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 思维导图 */}
            <div className="lg:col-span-2">
              <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 shadow-strong bg-white dark:bg-gray-900 floating-card mindmap-container h-full">
                <CardHeader className="border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50 pb-4">
                  <CardTitle className="flex items-center gap-3 text-lg font-semibold text-gray-900 dark:text-gray-100">
                    <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700">
                      <Brain className="w-4 h-4 text-gray-700 dark:text-gray-300" />
                    </div>
                    思维导图视图
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="h-[calc(100vh-380px)] min-h-[500px]">
                    <MindElixirReact
                      data={mapData}
                      plugins={plugins}
                      options={options}
                      className="h-full w-full"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 大纲视图 */}
            <div className="lg:col-span-1">
              <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 shadow-strong bg-white dark:bg-gray-900 floating-card h-full">
                <CardHeader className="border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/50 pb-4">
                  <CardTitle className="flex items-center gap-3 text-lg font-semibold text-gray-900 dark:text-gray-100">
                    <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700">
                      <List className="w-4 h-4 text-gray-700 dark:text-gray-300" />
                    </div>
                    大纲视图
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[calc(100vh-430px)] min-h-[450px] overflow-auto custom-scrollbar">
                    <Outliner data={mapData.nodeData.children!} />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>

      {/* 底部信息 */}
      <div className="border-t border-gray-200/50 dark:border-gray-800/50 bg-gray-50/80 dark:bg-gray-900/80 backdrop-blur-sm mt-auto">
        <div className="container mx-auto px-6 py-4">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-xs text-gray-500 dark:text-gray-400 mobile-text-center">
            <div className="flex items-center gap-4">
              <span className="font-medium text-gray-700 dark:text-gray-300">由 Mind Elixir Cloud 强力驱动</span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyLink}
                className="h-7 px-3 text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 btn-hover"
              >
                <Share2 className="w-3.5 h-3.5 mr-1.5" />
                分享此思维导图
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
