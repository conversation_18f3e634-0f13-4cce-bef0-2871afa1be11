'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'
import connect from '@/connect'
import { MindElixirData, Options } from 'mind-elixir'
import { MindMapItem } from '@/models/list'
// @ts-ignore
import nodeMenu from '@mind-elixir/node-menu-neo'
import { Outliner } from 'react-outliner-neo'
import 'react-outliner-neo/style.css'
import './share-page.css'
import {
  Brain,
  List,
  Maximize2,
  Minimize2,
  Calendar,
  Eye,
  Share2,
  Copy,
  Check
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

const MindElixirReact = dynamic(() => import('@/components/MindElixirReact'), {
  ssr: false,
  loading: () => <LoadingSkeleton />,
})

// 加载骨架屏组件
function LoadingSkeleton() {
  return (
    <div className="min-h-screen gradient-bg">
      <div className="container mx-auto px-4 py-8">
        {/* 头部骨架 */}
        <div className="mb-8">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded-lg w-1/3 mb-4 loading-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2 loading-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/6 loading-pulse"></div>
        </div>

        {/* 内容骨架 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-xl loading-pulse shadow-soft"></div>
          </div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded loading-pulse"></div>
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded loading-pulse shadow-soft"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

type ViewMode = 'mindmap' | 'outline' | 'split'

export default function MapSharePage() {
  const params = useParams()
  const router = useRouter()
  const [mapData, setMapData] = useState<MindElixirData | undefined>(undefined)
  const [mapItem, setMapItem] = useState<MindMapItem | undefined>(undefined)
  const [viewMode, setViewMode] = useState<ViewMode>('split')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)

  const plugins = [nodeMenu]
  const options: Partial<Options> = {
    direction: 2,
    draggable: false,
    editable: false,
    contextMenu: false,
    toolBar: true,
    keypress: false,
  }

  const mapId = params.id as string

  useEffect(() => {
    const fetchMap = async () => {
      try {
        setLoading(true)
        const res = await connect.get<never, { data: MindMapItem }>(
          `/api/public/${mapId}`
        )
        setMapItem(res.data)
        setMapData(res.data.content)
      } catch (error) {
        console.error('Failed to fetch map:', error)
        router.push('/404')
      } finally {
        setLoading(false)
      }
    }

    if (mapId) {
      fetchMap()
    }
  }, [mapId, router])

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return <LoadingSkeleton />
  }

  if (!mapData || !mapItem) {
    return (
      <div className="min-h-screen flex items-center justify-center gradient-bg">
        <div className="text-center glass-effect p-8 rounded-2xl shadow-strong">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 gradient-text">思维导图未找到</h1>
          <p className="text-gray-600 dark:text-gray-400">请检查链接是否正确</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "min-h-screen gradient-bg transition-all duration-300",
      isFullscreen && "fixed inset-0 z-50"
    )}>
      {/* 头部信息 */}
      <div className="border-b border-white/20 glass-effect">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* 标题和元信息 */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 rounded-lg bg-blue-500/10 dark:bg-blue-400/10">
                  <Brain className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h1 className="text-2xl lg:text-3xl font-bold gradient-text">
                  {mapItem.name}
                </h1>
                {mapItem.public && (
                  <Badge variant="secondary" className="status-badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 shadow-soft">
                    <Eye className="w-3 h-3 mr-1" />
                    公开
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>更新于 {formatDate(mapItem.updatedAt || mapItem.date)}</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-2 mobile-stack mobile-full-width">
              {/* 视图切换 */}
              <div className="flex items-center toolbar-glass rounded-lg p-1 shadow-soft">
                <Button
                  variant={viewMode === 'mindmap' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('mindmap')}
                  className="h-8 btn-hover"
                >
                  <Brain className="w-4 h-4 mr-1" />
                  思维导图
                </Button>
                <Button
                  variant={viewMode === 'outline' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('outline')}
                  className="h-8 btn-hover"
                >
                  <List className="w-4 h-4 mr-1" />
                  大纲
                </Button>
                <Button
                  variant={viewMode === 'split' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('split')}
                  className="h-8 btn-hover"
                >
                  分屏
                </Button>
              </div>

              {/* 全屏切换 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="toolbar-glass border-white/20 btn-hover shadow-soft"
              >
                {isFullscreen ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </Button>

              {/* 分享按钮 */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyLink}
                className="toolbar-glass border-white/20 btn-hover shadow-soft"
              >
                {copied ? (
                  <Check className="w-4 h-4 mr-1 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 mr-1" />
                )}
                {copied ? '已复制' : '复制链接'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-8">
        {viewMode === 'mindmap' && (
          <Card className="overflow-hidden border-0 shadow-strong glass-effect floating-card mindmap-container">
            <CardContent className="p-0">
              <div className="h-[calc(100vh-200px)] min-h-[600px]">
                <MindElixirReact
                  data={mapData}
                  plugins={plugins}
                  options={options}
                  className="h-full w-full"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {viewMode === 'outline' && (
          <Card className="overflow-hidden border-0 shadow-strong glass-effect floating-card">
            <CardHeader className="border-b border-gray-200/50 dark:border-gray-700/50">
              <CardTitle className="flex items-center gap-2 gradient-text">
                <List className="w-5 h-5" />
                思维导图大纲
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="h-[calc(100vh-300px)] min-h-[500px] overflow-auto custom-scrollbar outliner-container">
                <Outliner data={mapData.nodeData.children!} />
              </div>
            </CardContent>
          </Card>
        )}

        {viewMode === 'split' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 思维导图 */}
            <div className="lg:col-span-2">
              <Card className="overflow-hidden border-0 shadow-strong glass-effect floating-card mindmap-container h-full">
                <CardHeader className="border-b border-gray-200/50 dark:border-gray-700/50 pb-4">
                  <CardTitle className="flex items-center gap-2 gradient-text">
                    <Brain className="w-5 h-5" />
                    思维导图视图
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="h-[calc(100vh-300px)] min-h-[500px]">
                    <MindElixirReact
                      data={mapData}
                      plugins={plugins}
                      options={options}
                      className="h-full w-full"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 大纲视图 */}
            <div className="lg:col-span-1">
              <Card className="overflow-hidden border-0 shadow-strong glass-effect floating-card h-full">
                <CardHeader className="border-b border-gray-200/50 dark:border-gray-700/50 pb-4">
                  <CardTitle className="flex items-center gap-2 gradient-text">
                    <List className="w-5 h-5" />
                    大纲视图
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="h-[calc(100vh-350px)] min-h-[450px] overflow-auto custom-scrollbar outliner-container">
                    <Outliner data={mapData.nodeData.children!} />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>

      {/* 底部信息 */}
      <div className="border-t border-white/20 glass-effect mt-auto">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-sm text-gray-600 dark:text-gray-400 mobile-text-center">
            <div className="flex items-center gap-4">
              <span className="gradient-text font-medium">由 Mind Elixir Cloud 强力驱动</span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyLink}
                className="h-8 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 btn-hover"
              >
                <Share2 className="w-4 h-4 mr-1" />
                分享此思维导图
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
